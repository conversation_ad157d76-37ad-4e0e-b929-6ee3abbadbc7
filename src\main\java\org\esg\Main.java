package org.esg;

import lombok.Getter;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.esg.Manager.InvulnerabilityManager;
import org.esg.Manager.KnockbackImmunityManager;
import org.esg.Manager.PvPTagManager;
import org.esg.Manager.ScoreboardManager;
import org.esg.api.ArmorAPI;
import org.esg.api.WeaponAPI;
import org.esg.commands.ArmorCommand;
import org.esg.commands.KnockbackCommand;
import org.esg.commands.StatsCommand;
import org.esg.commands.WeaponCommand;
import org.esg.listeners.*;
import org.esg.listeners.WeaponStatsListener;
import org.esg.utils.ItemAnimationUtils;
import org.esg.utils.AnimationBlocker;

import org.esg.stats.StatsManager;
import org.esg.systems.WallClimbSystem;
import org.esg.ui.EnhancedHUD;

/**
 * Classe principal do plugin.
 */
public final class Main extends JavaPlugin {
    @Getter private static Main plugin;
    @Getter private static WallClimbSystem wallClimbSystem;


    @Override
    public void onEnable() {
        plugin = this;

        // Save and reload config to ensure all settings are available
        saveDefaultConfig();
        reloadConfig();
        getLogger().info("Configuration loaded successfully");

        registerListeners();
        registerCommands();
        initializeSystems();
        startHUDForPlayers();
        startInvulnerabilityManager();

        // Registrar o bloqueador de animações
        AnimationBlocker.register(this);

        // Registrar o menu de armaduras
        org.esg.ui.ArmorMenu.register();

        // Informar que as APIs estão disponíveis
        getLogger().info("WeaponAPI e ArmorAPI estão disponíveis para outros plugins!");
        getLogger().info("Plugin enabled successfully with knockback immunity system!");
    }

    /**
     * Obtém a instância da API de armas.
     * Este método é usado por outros plugins para acessar a API.
     *
     * @return A classe WeaponAPI
     */
    public static Class<?> getWeaponAPI() {
        return WeaponAPI.class;
    }

    /**
     * Obtém a instância da API de armaduras.
     * Este método é usado por outros plugins para acessar a API.
     *
     * @return A classe ArmorAPI
     */
    public static Class<?> getArmorAPI() {
        return ArmorAPI.class;
    }

    private void registerListeners() {
        getServer().getPluginManager().registerEvents(new ShootListener(), this);
        getServer().getPluginManager().registerEvents(new ReloadListener(), this);
        getServer().getPluginManager().registerEvents(new WeaponHeldListener(), this);
        getServer().getPluginManager().registerEvents(new PlayerJoinListener(), this);
        getServer().getPluginManager().registerEvents(new WeaponMenuListener(), this);
        getServer().getPluginManager().registerEvents(new StatsListener(), this);
        getServer().getPluginManager().registerEvents(new ArmorListener(), this);
        getServer().getPluginManager().registerEvents(new HungerListener(), this);
        getServer().getPluginManager().registerEvents(new WeaponSwitchListener(), this);
        getServer().getPluginManager().registerEvents(new PvPTagListener(), this);
        getServer().getPluginManager().registerEvents(new ScoreboardListener(), this);
        getServer().getPluginManager().registerEvents(new WeaponStatsListener(), this);
        getServer().getPluginManager().registerEvents(new ItemAnimationUtils(), this);
        getServer().getPluginManager().registerEvents(new AmmoSyncListener(), this);
        getServer().getPluginManager().registerEvents(new SniperScopeListener(), this);
        getServer().getPluginManager().registerEvents(new ProjectileInterferenceListener(), this);
    }

    private void registerCommands() {
        getCommand("weapon").setExecutor(new WeaponCommand());
        getCommand("stats").setExecutor(new StatsCommand());
        getCommand("armor").setExecutor(new ArmorCommand());
        getCommand("knockback").setExecutor(new KnockbackCommand());
    }

    private void initializeSystems() {
        StatsManager.initialize(this);
        wallClimbSystem = new WallClimbSystem(this);
        getServer().getPluginManager().registerEvents(wallClimbSystem, this);
        ScoreboardManager.initialize(this);
        PvPTagManager.initialize(this);
        KnockbackImmunityManager.initialize();
    }

    private void startHUDForPlayers() {
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : getServer().getOnlinePlayers()) {
                    EnhancedHUD.startHUD(player);
                }
            }
        }.runTaskLater(this, 20L);
    }

    private void startInvulnerabilityManager() {
        new BukkitRunnable() {
            @Override
            public void run() {
                InvulnerabilityManager.decrementInvulTicks();
            }
        }.runTaskTimer(this, 0L, 1L);
    }

    @Override
    public void onDisable() {
        // Clean up knockback immunity system
        KnockbackImmunityManager.shutdown();

        getLogger().info("Plugin disabled successfully!");
    }
}